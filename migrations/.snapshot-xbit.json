{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "gen_random_uuid()", "mappedType": "uuid"}, "category_code": {"name": "category_code", "type": "<PERSON><PERSON><PERSON>(100)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 100, "mappedType": "string"}, "category_name": {"name": "category_name", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 255, "mappedType": "string"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "notification_types", "schema": "public", "indexes": [{"columnNames": ["category_code"], "composite": false, "keyName": "notification_types_category_code_unique", "constraint": true, "primary": false, "unique": true}, {"keyName": "notification_types_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}}, "name": "real_user", "schema": "public", "indexes": [{"keyName": "real_user_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "auth_provider": {"name": "auth_provider", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["TELEGRAM", "CHAIN_EVM", "CHAIN_SOL", "CHAIN_TRON", "GOOGLE", "EMAIL"], "mappedType": "enum"}, "telegram_id": {"name": "telegram_id", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 255, "mappedType": "string"}, "telegram_username": {"name": "telegram_username", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 255, "mappedType": "string"}, "telegram_chat_id": {"name": "telegram_chat_id", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "bigint"}, "wallet_address": {"name": "wallet_address", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 255, "mappedType": "string"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 255, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "sub_org_id": {"name": "sub_org_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}, "turnkey_root_user_id": {"name": "turnkey_root_user_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}, "referrer_code": {"name": "referrer_code", "type": "<PERSON><PERSON><PERSON>(20)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 20, "mappedType": "string"}, "real_user_id": {"name": "real_user_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}, "google_id": {"name": "google_id", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 255, "mappedType": "string"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 255, "mappedType": "string"}, "avatar": {"name": "avatar", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "is_exported_wallet": {"name": "is_exported_wallet", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}}, "name": "user", "schema": "public", "indexes": [{"columnNames": ["telegram_id"], "composite": false, "keyName": "user_telegram_id_unique", "constraint": true, "primary": false, "unique": true}, {"columnNames": ["telegram_chat_id"], "composite": false, "keyName": "user_telegram_chat_id_unique", "constraint": true, "primary": false, "unique": true}, {"columnNames": ["wallet_address"], "composite": false, "keyName": "user_wallet_address_unique", "constraint": true, "primary": false, "unique": true}, {"columnNames": ["sub_org_id"], "composite": false, "keyName": "user_sub_org_id_unique", "constraint": true, "primary": false, "unique": true}, {"columnNames": ["turnkey_root_user_id"], "composite": false, "keyName": "user_turnkey_root_user_id_unique", "constraint": true, "primary": false, "unique": true}, {"columnNames": ["referrer_code"], "composite": false, "keyName": "user_referrer_code_unique", "constraint": true, "primary": false, "unique": true}, {"columnNames": ["google_id"], "composite": false, "keyName": "user_google_id_unique", "constraint": true, "primary": false, "unique": true}, {"columnNames": ["email"], "composite": false, "keyName": "user_email_unique", "constraint": true, "primary": false, "unique": true}, {"keyName": "user_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"user_real_user_id_foreign": {"constraintName": "user_real_user_id_foreign", "columnNames": ["real_user_id"], "localTableName": "public.user", "referencedColumnNames": ["id"], "referencedTableName": "public.real_user", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "user_id": {"name": "user_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "user_address": {"name": "user_address", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 255, "mappedType": "string"}, "agent_address": {"name": "agent_address", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 255, "default": "false", "mappedType": "string"}, "expired_at": {"name": "expired_at", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "bigint"}, "encrypted_private_key": {"name": "encrypted_private_key", "type": "<PERSON><PERSON><PERSON>(511)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 511, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}}, "name": "perpetual_agent", "schema": "public", "indexes": [{"columnNames": ["agent_address"], "composite": false, "keyName": "perpetual_agent_agent_address_unique", "constraint": true, "primary": false, "unique": true}, {"keyName": "perpetual_agent_agent_address_index", "columnNames": ["agent_address"], "composite": false, "constraint": false, "primary": false, "unique": false}, {"keyName": "perpetual_agent_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"perpetual_agent_user_id_foreign": {"constraintName": "perpetual_agent_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.perpetual_agent", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "user_id": {"name": "user_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "fingerprint": {"name": "fingerprint", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 255, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "real_user_id": {"name": "real_user_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}}, "name": "user_device", "schema": "public", "indexes": [{"keyName": "user_device_user_id_fingerprint_unique", "columnNames": ["user_id", "fingerprint"], "composite": true, "constraint": true, "primary": false, "unique": true}, {"keyName": "user_device_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"user_device_user_id_foreign": {"constraintName": "user_device_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.user_device", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "updateRule": "cascade"}, "user_device_real_user_id_foreign": {"constraintName": "user_device_real_user_id_foreign", "columnNames": ["real_user_id"], "localTableName": "public.user_device", "referencedColumnNames": ["id"], "referencedTableName": "public.real_user", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "user_id": {"name": "user_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "device_id": {"name": "device_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "uuid"}, "activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 255, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}}, "name": "activity_log", "schema": "public", "indexes": [{"keyName": "activity_log_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"activity_log_user_id_foreign": {"constraintName": "activity_log_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.activity_log", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "updateRule": "cascade"}, "activity_log_device_id_foreign": {"constraintName": "activity_log_device_id_foreign", "columnNames": ["device_id"], "localTableName": "public.activity_log", "referencedColumnNames": ["id"], "referencedTableName": "public.user_device", "deleteRule": "set null", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "user_id": {"name": "user_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "chain": {"name": "chain", "type": "<PERSON><PERSON><PERSON>(15)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 15, "enumItems": ["EVM", "SOLANA", "TRON", "ARB"], "mappedType": "enum"}, "wallet_address": {"name": "wallet_address", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 255, "mappedType": "string"}, "wallet_id": {"name": "wallet_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "wallet_account_id": {"name": "wallet_account_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "hd_path": {"name": "hd_path", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 255, "mappedType": "string"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 255, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}}, "name": "user_embedded_wallet", "schema": "public", "indexes": [{"columnNames": ["wallet_address"], "composite": false, "keyName": "user_embedded_wallet_wallet_address_index", "constraint": false, "primary": false, "unique": false}, {"keyName": "user_embedded_wallet_wallet_address_chain_unique", "columnNames": ["wallet_address", "chain"], "composite": true, "constraint": true, "primary": false, "unique": true}, {"keyName": "user_embedded_wallet_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"user_embedded_wallet_user_id_foreign": {"constraintName": "user_embedded_wallet_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.user_embedded_wallet", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "gen_random_uuid()", "mappedType": "uuid"}, "user_id": {"name": "user_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "secret_key": {"name": "secret_key", "type": "<PERSON><PERSON><PERSON>(100)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 100, "mappedType": "string"}, "recovery_codes_hashed": {"name": "recovery_codes_hashed", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "is_enabled": {"name": "is_enabled", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "user_google_authenticator", "schema": "public", "indexes": [{"columnNames": ["user_id"], "composite": false, "keyName": "user_google_authenticator_user_id_unique", "constraint": true, "primary": false, "unique": true}, {"keyName": "user_google_authenticator_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"user_google_authenticator_user_id_foreign": {"constraintName": "user_google_authenticator_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.user_google_authenticator", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "user_id": {"name": "user_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "chain": {"name": "chain", "type": "<PERSON><PERSON><PERSON>(15)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 15, "enumItems": ["EVM", "SOLANA", "TRON", "ARB"], "mappedType": "enum"}, "wallet_address": {"name": "wallet_address", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 255, "mappedType": "string"}, "encrypted_private_key": {"name": "encrypted_private_key", "type": "<PERSON><PERSON><PERSON>(511)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 511, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}}, "name": "user_managed_wallet", "schema": "public", "indexes": [{"keyName": "user_managed_wallet_wallet_address_chain_unique", "columnNames": ["wallet_address", "chain"], "composite": true, "constraint": true, "primary": false, "unique": true}, {"keyName": "user_managed_wallet_user_id_chain_unique", "columnNames": ["user_id", "chain"], "composite": true, "constraint": true, "primary": false, "unique": true}, {"keyName": "user_managed_wallet_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"user_managed_wallet_user_id_foreign": {"constraintName": "user_managed_wallet_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.user_managed_wallet", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "gen_random_uuid()", "mappedType": "uuid"}, "user_id": {"name": "user_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "notification_type_code": {"name": "notification_type_code", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "channel": {"name": "channel", "type": "<PERSON><PERSON><PERSON>(20)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 20, "mappedType": "string"}, "is_enabled": {"name": "is_enabled", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "user_notification_preferences", "schema": "public", "indexes": [{"keyName": "user_notification_preferences_user_id_notificatio_4f93b_unique", "columnNames": ["user_id", "notification_type_code", "channel"], "composite": true, "constraint": true, "primary": false, "unique": true}, {"keyName": "user_notification_preferences_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"user_notification_preferences_user_id_foreign": {"constraintName": "user_notification_preferences_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.user_notification_preferences", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "updateRule": "cascade"}, "user_notification_preferences_notification_type_code_foreign": {"constraintName": "user_notification_preferences_notification_type_code_foreign", "columnNames": ["notification_type_code"], "localTableName": "public.user_notification_preferences", "referencedColumnNames": ["id"], "referencedTableName": "public.notification_types", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "user_id": {"name": "user_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "set_referral": {"name": "set_referral", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "referral_code": {"name": "referral_code", "type": "<PERSON><PERSON><PERSON>(55)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 55, "mappedType": "string"}, "set_fee_builder": {"name": "set_fee_builder", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "fee_builder_address": {"name": "fee_builder_address", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 255, "mappedType": "string"}, "fee_builder_percent": {"name": "fee_builder_percent", "type": "numeric(10,0)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "precision": 10, "scale": 0, "mappedType": "decimal"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}}, "name": "user_perpetual_status", "schema": "public", "indexes": [{"keyName": "user_perpetual_status_user_id_unique", "columnNames": ["user_id"], "composite": false, "constraint": true, "primary": false, "unique": true}, {"keyName": "user_perpetual_status_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"user_perpetual_status_user_id_foreign": {"constraintName": "user_perpetual_status_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.user_perpetual_status", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "user_id": {"name": "user_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(60)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 60, "mappedType": "string"}, "nickname": {"name": "nickname", "type": "<PERSON><PERSON><PERSON>(60)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 60, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "user_withdrawal_whitelist_addresses", "schema": "public", "indexes": [{"keyName": "user_withdrawal_whitelist_addresses_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"user_withdrawal_whitelist_addresses_user_id_foreign": {"constraintName": "user_withdrawal_whitelist_addresses_user_id_foreign", "columnNames": ["user_id"], "localTableName": "public.user_withdrawal_whitelist_addresses", "referencedColumnNames": ["id"], "referencedTableName": "public.user", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 255, "mappedType": "string"}, "encrypted_private_key": {"name": "encrypted_private_key", "type": "<PERSON><PERSON><PERSON>(511)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 511, "mappedType": "string"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}}, "name": "vault_master_wallet", "schema": "public", "indexes": [{"columnNames": ["address"], "composite": false, "keyName": "vault_master_wallet_address_unique", "constraint": true, "primary": false, "unique": true}, {"keyName": "vault_master_wallet_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "vault_master_wallet_id": {"name": "vault_master_wallet_id", "type": "uuid", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "uuid"}, "agent_address": {"name": "agent_address", "type": "<PERSON><PERSON><PERSON>(255)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 255, "mappedType": "string"}, "encrypted_private_key": {"name": "encrypted_private_key", "type": "<PERSON><PERSON><PERSON>(511)", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 511, "mappedType": "string"}, "expired_at": {"name": "expired_at", "type": "bigint", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "bigint"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}}, "name": "vault_agent_wallet", "schema": "public", "indexes": [{"columnNames": ["vault_master_wallet_id"], "composite": false, "keyName": "vault_agent_wallet_vault_master_wallet_id_unique", "constraint": true, "primary": false, "unique": true}, {"columnNames": ["agent_address"], "composite": false, "keyName": "vault_agent_wallet_agent_address_unique", "constraint": true, "primary": false, "unique": true}, {"keyName": "vault_agent_wallet_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"vault_agent_wallet_vault_master_wallet_id_foreign": {"constraintName": "vault_agent_wallet_vault_master_wallet_id_foreign", "columnNames": ["vault_master_wallet_id"], "localTableName": "public.vault_agent_wallet", "referencedColumnNames": ["id"], "referencedTableName": "public.vault_master_wallet", "updateRule": "cascade"}}, "nativeEnums": {}}], "nativeEnums": {}}